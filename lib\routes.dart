import 'package:tadabbur_ai_app/library.dart';

class _Routes {
  static final _Routes _instance = _Routes._internal();
  final List<GetPage> _routes = [];

  String initialRoute = '/home';

  factory _Routes() {
    return _instance;
  }

  _Routes._internal();

  List<GetPage> getAllRoutes() {
    return _instance._routes;
  }

  void addRoutes(List<GetPage> routes) {
    _routes.addAll(routes);
  }

  void bindingModuleRoutes() {
    _routes.clear();
    HomeScreenRoute.routes();
    LoginScreenRoute.routes();
  }
}

final routing = _Routes();
