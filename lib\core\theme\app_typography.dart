import 'package:flutter/material.dart';

/// App typography system based on Material Design 3 type scale.
/// 
/// This class provides text themes and custom text styles for the app,
/// including support for Arabic text and app-specific use cases.
/// 
/// Usage:
/// ```dart
/// // In your MaterialApp
/// theme: ThemeData(textTheme: AppTypography.textTheme),
/// 
/// // Accessing text styles in widgets
/// Text('Hello', style: AppTypography.headlineLarge)
/// Text('مرحبا', style: AppTypography.arabicLarge)
/// Text('Chat message', style: AppTypography.chatUserMessage)
/// ```
class AppTypography {
  AppTypography._(); // Private constructor to prevent instantiation

  /// Primary font family (Inter)
  static const String primaryFontFamily = 'Inter';
  
  /// Arabic font family (Noto Sans Arabic)
  static const String arabicFontFamily = 'Noto Sans Arabic';
  
  /// System fallback fonts
  static const String fallbackFontFamily = 'system-ui, -apple-system, BlinkMacSystemFont, sans-serif';

  /// Material Design 3 text theme
  static const TextTheme textTheme = TextTheme(
    // Display styles
    displayLarge: TextStyle(
      fontFamily: primaryFontFamily,
      fontSize: 57,
      fontWeight: FontWeight.w400,
      height: 64 / 57, // lineHeight / fontSize
      letterSpacing: -0.25,
    ),
    displayMedium: TextStyle(
      fontFamily: primaryFontFamily,
      fontSize: 45,
      fontWeight: FontWeight.w400,
      height: 52 / 45,
      letterSpacing: 0,
    ),
    displaySmall: TextStyle(
      fontFamily: primaryFontFamily,
      fontSize: 36,
      fontWeight: FontWeight.w400,
      height: 44 / 36,
      letterSpacing: 0,
    ),
    
    // Headline styles
    headlineLarge: TextStyle(
      fontFamily: primaryFontFamily,
      fontSize: 32,
      fontWeight: FontWeight.w400,
      height: 40 / 32,
      letterSpacing: 0,
    ),
    headlineMedium: TextStyle(
      fontFamily: primaryFontFamily,
      fontSize: 28,
      fontWeight: FontWeight.w400,
      height: 36 / 28,
      letterSpacing: 0,
    ),
    headlineSmall: TextStyle(
      fontFamily: primaryFontFamily,
      fontSize: 24,
      fontWeight: FontWeight.w400,
      height: 32 / 24,
      letterSpacing: 0,
    ),
    
    // Title styles
    titleLarge: TextStyle(
      fontFamily: primaryFontFamily,
      fontSize: 22,
      fontWeight: FontWeight.w400,
      height: 28 / 22,
      letterSpacing: 0,
    ),
    titleMedium: TextStyle(
      fontFamily: primaryFontFamily,
      fontSize: 16,
      fontWeight: FontWeight.w500,
      height: 24 / 16,
      letterSpacing: 0.15,
    ),
    titleSmall: TextStyle(
      fontFamily: primaryFontFamily,
      fontSize: 14,
      fontWeight: FontWeight.w500,
      height: 20 / 14,
      letterSpacing: 0.1,
    ),
    
    // Label styles
    labelLarge: TextStyle(
      fontFamily: primaryFontFamily,
      fontSize: 14,
      fontWeight: FontWeight.w500,
      height: 20 / 14,
      letterSpacing: 0.1,
    ),
    labelMedium: TextStyle(
      fontFamily: primaryFontFamily,
      fontSize: 12,
      fontWeight: FontWeight.w500,
      height: 16 / 12,
      letterSpacing: 0.5,
    ),
    labelSmall: TextStyle(
      fontFamily: primaryFontFamily,
      fontSize: 11,
      fontWeight: FontWeight.w500,
      height: 16 / 11,
      letterSpacing: 0.5,
    ),
    
    // Body styles
    bodyLarge: TextStyle(
      fontFamily: primaryFontFamily,
      fontSize: 16,
      fontWeight: FontWeight.w400,
      height: 24 / 16,
      letterSpacing: 0.5,
    ),
    bodyMedium: TextStyle(
      fontFamily: primaryFontFamily,
      fontSize: 14,
      fontWeight: FontWeight.w400,
      height: 20 / 14,
      letterSpacing: 0.25,
    ),
    bodySmall: TextStyle(
      fontFamily: primaryFontFamily,
      fontSize: 12,
      fontWeight: FontWeight.w400,
      height: 16 / 12,
      letterSpacing: 0.4,
    ),
  );

  // Arabic text styles
  /// Large Arabic text style - for Quranic verses and main Arabic content
  static const TextStyle arabicLarge = TextStyle(
    fontFamily: arabicFontFamily,
    fontSize: 18,
    fontWeight: FontWeight.w400,
    height: 28 / 18,
    letterSpacing: 0,
  );

  /// Medium Arabic text style - for translations and secondary Arabic text
  static const TextStyle arabicMedium = TextStyle(
    fontFamily: arabicFontFamily,
    fontSize: 16,
    fontWeight: FontWeight.w400,
    height: 24 / 16,
    letterSpacing: 0,
  );

  /// Small Arabic text style - for references and tertiary Arabic text
  static const TextStyle arabicSmall = TextStyle(
    fontFamily: arabicFontFamily,
    fontSize: 14,
    fontWeight: FontWeight.w400,
    height: 20 / 14,
    letterSpacing: 0,
  );

  // App-specific text styles
  /// Chat user message text style
  static const TextStyle chatUserMessage = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: 16,
    fontWeight: FontWeight.w400,
    height: 22 / 16,
    letterSpacing: 0.25,
  );

  /// Chat AI message text style
  static const TextStyle chatAiMessage = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: 16,
    fontWeight: FontWeight.w400,
    height: 24 / 16,
    letterSpacing: 0.25,
  );

  /// Quranic verse Arabic text style
  static const TextStyle verseArabic = TextStyle(
    fontFamily: arabicFontFamily,
    fontSize: 20,
    fontWeight: FontWeight.w400,
    height: 32 / 20,
    letterSpacing: 0,
  );

  /// Verse translation text style
  static const TextStyle verseTranslation = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: 16,
    fontWeight: FontWeight.w400,
    height: 24 / 16,
    letterSpacing: 0.25,
    fontStyle: FontStyle.italic,
  );

  /// Note title text style
  static const TextStyle noteTitle = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: 18,
    fontWeight: FontWeight.w500,
    height: 24 / 18,
    letterSpacing: 0.15,
  );

  /// Category card text style
  static const TextStyle categoryCard = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: 16,
    fontWeight: FontWeight.w500,
    height: 20 / 16,
    letterSpacing: 0.15,
  );

  /// Bottom navigation label text style
  static const TextStyle bottomNavLabel = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: 12,
    fontWeight: FontWeight.w500,
    height: 16 / 12,
    letterSpacing: 0.5,
  );
}
