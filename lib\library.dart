// library.dart: Barrel file for exports
export 'package:flutter/material.dart';

// PACKAGE EXPORTS
export 'package:get/get.dart';
export 'package:get_storage/get_storage.dart';

// CONFIG & CORE
export 'package:tadabbur_ai_app/app.dart';
export 'package:tadabbur_ai_app/config.dart';
export 'package:tadabbur_ai_app/static_data.dart';
export 'package:tadabbur_ai_app/init.dart';
export 'package:tadabbur_ai_app/color_theme.dart';
export 'package:tadabbur_ai_app/core/theme/theme.dart';

// ROUTES
export 'package:tadabbur_ai_app/routes.dart';

// UTILS
export 'package:tadabbur_ai_app/utils/constant.dart';
export 'package:tadabbur_ai_app/utils/general_function.dart';

// WIDGETS
export 'package:tadabbur_ai_app/general_widgets/loading_dialog.dart';
export 'package:tadabbur_ai_app/general_widgets/snackbar.dart';
export 'package:tadabbur_ai_app/general_widgets/text_widget.dart';
export 'package:tadabbur_ai_app/app_drawer/app_drawer.dart';
export 'package:tadabbur_ai_app/bottombar/bottombar.dart';

// SERVICES
export 'package:tadabbur_ai_app/services/home_services.dart';
export 'package:tadabbur_ai_app/services/services_binding.dart';

// TRANSLATION
export 'package:tadabbur_ai_app/translation/localization.dart';
export 'package:tadabbur_ai_app/translation/en.dart';
export 'package:tadabbur_ai_app/translation/ms.dart';

// MODULES (DERIVATIVE EXPORT)
export 'package:tadabbur_ai_app/modules/DummyModules/library.dart';

// SCREENS (DERIVATIVE EXPORT)
export 'package:tadabbur_ai_app/screens/HomeScreen/library.dart';
export 'package:tadabbur_ai_app/screens/SettingsScreen/library.dart';
export 'package:tadabbur_ai_app/screens/LoginScreen/library.dart';
