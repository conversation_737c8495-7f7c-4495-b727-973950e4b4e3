import 'package:flutter/material.dart';
import 'theme.dart';

/// Example demonstrating how to use the Tadabbur AI theme system.
/// 
/// This file shows practical examples of using colors, text styles,
/// and theme utilities throughout your app.
class ThemeExampleScreen extends StatelessWidget {
  const ThemeExampleScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Theme System Demo'),
        actions: [
          IconButton(
            icon: Icon(context.isDarkTheme ? Icons.light_mode : Icons.dark_mode),
            onPressed: () {
              // Toggle theme (you'll need to implement theme switching in your app)
            },
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Typography Examples
            _buildTypographySection(context),
            const SizedBox(height: 32),
            
            // Color Examples
            _buildColorSection(context),
            const SizedBox(height: 32),
            
            // Component Examples
            _buildComponentSection(context),
            const SizedBox(height: 32),
            
            // Arabic Text Examples
            _buildArabicSection(context),
            const SizedBox(height: 32),
            
            // App-Specific Examples
            _buildAppSpecificSection(context),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {},
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildTypographySection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Typography Scale', style: AppTextStyles.headlineMedium),
        const SizedBox(height: 16),
        
        Text('Display Large', style: AppTextStyles.displayLarge),
        Text('Display Medium', style: AppTextStyles.displayMedium),
        Text('Display Small', style: AppTextStyles.displaySmall),
        const SizedBox(height: 8),
        
        Text('Headline Large', style: AppTextStyles.headlineLarge),
        Text('Headline Medium', style: AppTextStyles.headlineMedium),
        Text('Headline Small', style: AppTextStyles.headlineSmall),
        const SizedBox(height: 8),
        
        Text('Title Large', style: AppTextStyles.titleLarge),
        Text('Title Medium', style: AppTextStyles.titleMedium),
        Text('Title Small', style: AppTextStyles.titleSmall),
        const SizedBox(height: 8),
        
        Text('Body Large - Main content text', style: AppTextStyles.bodyLarge),
        Text('Body Medium - Secondary content', style: AppTextStyles.bodyMedium),
        Text('Body Small - Captions and small text', style: AppTextStyles.bodySmall),
        const SizedBox(height: 8),
        
        Text('Label Large', style: AppTextStyles.labelLarge),
        Text('Label Medium', style: AppTextStyles.labelMedium),
        Text('Label Small', style: AppTextStyles.labelSmall),
      ],
    );
  }

  Widget _buildColorSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Color System', style: AppTextStyles.headlineMedium),
        const SizedBox(height: 16),
        
        // Primary colors
        Row(
          children: [
            _buildColorSwatch('Primary', context.colors.primary, context.colors.onPrimary),
            const SizedBox(width: 8),
            _buildColorSwatch('Primary Container', context.colors.primaryContainer, context.colors.onPrimaryContainer),
          ],
        ),
        const SizedBox(height: 8),
        
        // Secondary colors
        Row(
          children: [
            _buildColorSwatch('Secondary', context.colors.secondary, context.colors.onSecondary),
            const SizedBox(width: 8),
            _buildColorSwatch('Secondary Container', context.colors.secondaryContainer, context.colors.onSecondaryContainer),
          ],
        ),
        const SizedBox(height: 8),
        
        // Surface colors
        Row(
          children: [
            _buildColorSwatch('Surface', context.colors.surface, context.colors.onSurface),
            const SizedBox(width: 8),
            _buildColorSwatch('Surface Variant', context.colors.surfaceVariant, context.colors.onSurfaceVariant),
          ],
        ),
      ],
    );
  }

  Widget _buildColorSwatch(String label, Color backgroundColor, Color textColor) {
    return Expanded(
      child: Container(
        height: 80,
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Center(
          child: Text(
            label,
            style: TextStyle(color: textColor, fontWeight: FontWeight.w500),
            textAlign: TextAlign.center,
          ),
        ),
      ),
    );
  }

  Widget _buildComponentSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Components', style: AppTextStyles.headlineMedium),
        const SizedBox(height: 16),
        
        // Buttons
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: [
            ElevatedButton(onPressed: () {}, child: const Text('Elevated')),
            TextButton(onPressed: () {}, child: const Text('Text')),
            OutlinedButton(onPressed: () {}, child: const Text('Outlined')),
          ],
        ),
        const SizedBox(height: 16),
        
        // Cards
        SurfaceContainer(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('Surface Container', style: AppTextStyles.titleMedium),
              const SizedBox(height: 8),
              Text('This is a surface container with the appropriate background color for the current theme.', 
                   style: AppTextStyles.bodyMedium),
            ],
          ),
        ),
        const SizedBox(height: 16),
        
        // Input field
        TextField(
          decoration: const InputDecoration(
            labelText: 'Enter text',
            hintText: 'Type something...',
          ),
        ),
      ],
    );
  }

  Widget _buildArabicSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Arabic Text Support', style: AppTextStyles.headlineMedium),
        const SizedBox(height: 16),
        
        ArabicText(
          'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ',
          style: AppTextStyles.verseArabic,
        ),
        const SizedBox(height: 8),
        
        Text(
          'In the name of Allah, the Most Gracious, the Most Merciful',
          style: AppTextStyles.verseTranslation,
        ),
        const SizedBox(height: 16),
        
        ArabicText(
          'النص العربي الكبير',
          style: AppTextStyles.arabicLarge,
        ),
        const SizedBox(height: 4),
        
        ArabicText(
          'النص العربي المتوسط',
          style: AppTextStyles.arabicMedium,
        ),
        const SizedBox(height: 4),
        
        ArabicText(
          'النص العربي الصغير',
          style: AppTextStyles.arabicSmall,
        ),
      ],
    );
  }

  Widget _buildAppSpecificSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('App-Specific Styles', style: AppTextStyles.headlineMedium),
        const SizedBox(height: 16),
        
        // Chat messages
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: context.colors.primaryContainer,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            'This is a user chat message',
            style: AppTextStyles.chatUserMessage.copyWith(
              color: context.colors.onPrimaryContainer,
            ),
          ),
        ),
        const SizedBox(height: 8),
        
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: context.colors.secondaryContainer,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            'This is an AI response message with more detailed information.',
            style: AppTextStyles.chatAiMessage.copyWith(
              color: context.colors.onSecondaryContainer,
            ),
          ),
        ),
        const SizedBox(height: 16),
        
        // Note title
        Text('Note Title Example', style: AppTextStyles.noteTitle),
        const SizedBox(height: 8),
        
        // Category card
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            color: context.colors.tertiaryContainer,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Text(
            'Category Card',
            style: AppTextStyles.categoryCard.copyWith(
              color: context.colors.onTertiaryContainer,
            ),
          ),
        ),
      ],
    );
  }
}
