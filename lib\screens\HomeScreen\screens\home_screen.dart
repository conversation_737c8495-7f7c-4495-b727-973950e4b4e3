// home_screen.dart: HomeScreen widget/controller

import 'package:tadabbur_ai_app/library.dart';

class HomeScreen extends GetView<HomeScreenController> {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('hello'.tr)),
      body: Center(
        child: ElevatedButton(
          onPressed: () => Get.toNamed(LoginScreenRoute.index),
          child: Text('Go to Settings'),
        ),
      ),
    );
  }
}
