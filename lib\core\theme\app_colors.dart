import 'package:flutter/material.dart';

/// App color schemes based on Material Design 3 color system.
/// 
/// This class provides both light and dark color schemes that follow
/// Material Design 3 guidelines and are generated from your design tokens.
/// 
/// Usage:
/// ```dart
/// // In your MaterialApp
/// theme: ThemeData(colorScheme: AppColors.lightColorScheme),
/// darkTheme: ThemeData(colorScheme: AppColors.darkColorScheme),
/// 
/// // Accessing colors in widgets
/// final colors = Theme.of(context).colorScheme;
/// Container(color: colors.primary)
/// ```
class AppColors {
  AppColors._(); // Private constructor to prevent instantiation

  /// Light theme color scheme
  static const ColorScheme lightColorScheme = ColorScheme(
    brightness: Brightness.light,
    
    // Primary colors
    primary: Color(0xFF00A651),
    onPrimary: Color(0xFFFFFFFF),
    primaryContainer: Color(0xFF7EF88A),
    onPrimaryContainer: Color(0xFF002113),
    
    // Secondary colors
    secondary: Color(0xFF516352),
    onSecondary: Color(0xFFFFFFFF),
    secondaryContainer: Color(0xFFD4E8D3),
    onSecondaryContainer: Color(0xFF0F1F12),
    
    // Tertiary colors
    tertiary: Color(0xFF3A656F),
    onTertiary: Color(0xFFFFFFFF),
    tertiaryContainer: Color(0xFFBEE9F6),
    onTertiaryContainer: Color(0xFF001F26),
    
    // Error colors
    error: Color(0xFFBA1A1A),
    onError: Color(0xFFFFFFFF),
    errorContainer: Color(0xFFFFDAD6),
    onErrorContainer: Color(0xFF410002),
    
    // Surface colors
    surface: Color(0xFFFEFBFF),
    onSurface: Color(0xFF1D1B20),
    surfaceVariant: Color(0xFFE0E3DC),
    onSurfaceVariant: Color(0xFF44474E),
    
    // Other colors
    outline: Color(0xFF74777F),
    outlineVariant: Color(0xFFC4C7C0),
    shadow: Color(0xFF000000),
    scrim: Color(0xFF000000),
    inverseSurface: Color(0xFF322F35),
    onInverseSurface: Color(0xFFF5EFF7),
    inversePrimary: Color(0xFF63DC6E),
    surfaceTint: Color(0xFF00A651),
  );

  /// Dark theme color scheme
  static const ColorScheme darkColorScheme = ColorScheme(
    brightness: Brightness.dark,
    
    // Primary colors
    primary: Color(0xFF63DC6E),
    onPrimary: Color(0xFF00391C),
    primaryContainer: Color(0xFF005D2C),
    onPrimaryContainer: Color(0xFF7EF88A),
    
    // Secondary colors
    secondary: Color(0xFFB8CCB8),
    onSecondary: Color(0xFF243427),
    secondaryContainer: Color(0xFF3A4B3B),
    onSecondaryContainer: Color(0xFFD4E8D3),
    
    // Tertiary colors
    tertiary: Color(0xFFA2CDDA),
    onTertiary: Color(0xFF04363F),
    tertiaryContainer: Color(0xFF204D56),
    onTertiaryContainer: Color(0xFFBEE9F6),
    
    // Error colors
    error: Color(0xFFFFB4AB),
    onError: Color(0xFF690005),
    errorContainer: Color(0xFF93000A),
    onErrorContainer: Color(0xFFFFDAD6),
    
    // Surface colors
    surface: Color(0xFF141218),
    onSurface: Color(0xFFE6E0E9),
    surfaceVariant: Color(0xFF44474E),
    onSurfaceVariant: Color(0xFFC4C7C0),
    
    // Other colors
    outline: Color(0xFF8E9199),
    outlineVariant: Color(0xFF44474E),
    shadow: Color(0xFF000000),
    scrim: Color(0xFF000000),
    inverseSurface: Color(0xFFE6E0E9),
    onInverseSurface: Color(0xFF322F35),
    inversePrimary: Color(0xFF00A651),
    surfaceTint: Color(0xFF63DC6E),
  );

  /// Extended surface colors for light theme
  /// These provide additional surface variations following Material Design 3
  static const Map<String, Color> lightSurfaceColors = {
    'surfaceDim': Color(0xFFDED8E1),
    'surfaceBright': Color(0xFFFEFBFF),
    'surfaceContainerLowest': Color(0xFFFFFFFF),
    'surfaceContainerLow': Color(0xFFF7F2FA),
    'surfaceContainer': Color(0xFFF1ECF4),
    'surfaceContainerHigh': Color(0xFFECE6F0),
    'surfaceContainerHighest': Color(0xFFE6E0E9),
  };

  /// Extended surface colors for dark theme
  static const Map<String, Color> darkSurfaceColors = {
    'surfaceDim': Color(0xFF141218),
    'surfaceBright': Color(0xFF3A383E),
    'surfaceContainerLowest': Color(0xFF0F0D13),
    'surfaceContainerLow': Color(0xFF1D1B20),
    'surfaceContainer': Color(0xFF211F26),
    'surfaceContainerHigh': Color(0xFF2B2930),
    'surfaceContainerHighest': Color(0xFF36343B),
  };

  /// Fixed colors (same in both light and dark themes)
  static const Map<String, Color> fixedColors = {
    // Primary fixed
    'primaryFixed': Color(0xFF7EF88A),
    'onPrimaryFixed': Color(0xFF002113),
    'primaryFixedDim': Color(0xFF63DC6E),
    'onPrimaryFixedVariant': Color(0xFF00803D),
    
    // Secondary fixed
    'secondaryFixed': Color(0xFFD4E8D3),
    'onSecondaryFixed': Color(0xFF0F1F12),
    'secondaryFixedDim': Color(0xFFB8CCB8),
    'onSecondaryFixedVariant': Color(0xFF3A4B3B),
    
    // Tertiary fixed
    'tertiaryFixed': Color(0xFFBEE9F6),
    'onTertiaryFixed': Color(0xFF001F26),
    'tertiaryFixedDim': Color(0xFFA2CDDA),
    'onTertiaryFixedVariant': Color(0xFF204D56),
  };
}
