import 'package:flutter/material.dart';
import 'app_colors.dart';
import 'app_typography.dart';

/// Extension methods for easy access to theme colors and text styles.
/// 
/// These extensions provide convenient access to the app's design system
/// from anywhere in the widget tree using BuildContext.
/// 
/// Usage:
/// ```dart
/// // In any widget with BuildContext
/// Container(color: context.colors.primary)
/// Text('Hello', style: context.textStyles.headlineLarge)
/// Text('مرحبا', style: context.textStyles.arabicLarge)
/// Container(color: context.surfaceColors.surfaceContainer)
/// ```
extension ThemeExtensions on BuildContext {
  /// Access to the current color scheme
  ColorScheme get colors => Theme.of(this).colorScheme;
  
  /// Access to the current text theme
  TextTheme get textTheme => Theme.of(this).textTheme;
  
  /// Access to custom text styles
  AppTextStyles get textStyles => AppTextStyles();
  
  /// Access to surface colors based on current theme brightness
  Map<String, Color> get surfaceColors {
    final brightness = Theme.of(this).brightness;
    return brightness == Brightness.light 
        ? AppColors.lightSurfaceColors 
        : AppColors.darkSurfaceColors;
  }
  
  /// Access to fixed colors (same in both themes)
  Map<String, Color> get fixedColors => AppColors.fixedColors;
  
  /// Check if current theme is dark
  bool get isDarkTheme => Theme.of(this).brightness == Brightness.dark;
  
  /// Check if current theme is light
  bool get isLightTheme => Theme.of(this).brightness == Brightness.light;
}

/// Custom text styles wrapper for easy access
class AppTextStyles {
  // Material Design 3 text styles (from TextTheme)
  static  TextStyle displayLarge = AppTypography.textTheme.displayLarge!;
  static  TextStyle displayMedium = AppTypography.textTheme.displayMedium!;
  static  TextStyle displaySmall = AppTypography.textTheme.displaySmall!;
  static  TextStyle headlineLarge = AppTypography.textTheme.headlineLarge!;
  static  TextStyle headlineMedium = AppTypography.textTheme.headlineMedium!;
  static  TextStyle headlineSmall = AppTypography.textTheme.headlineSmall!;
  static  TextStyle titleLarge = AppTypography.textTheme.titleLarge!;
  static  TextStyle titleMedium = AppTypography.textTheme.titleMedium!;
  static  TextStyle titleSmall = AppTypography.textTheme.titleSmall!;
  static  TextStyle labelLarge = AppTypography.textTheme.labelLarge!;
  static  TextStyle labelMedium = AppTypography.textTheme.labelMedium!;
  static  TextStyle labelSmall = AppTypography.textTheme.labelSmall!;
  static  TextStyle bodyLarge = AppTypography.textTheme.bodyLarge!;
  static  TextStyle bodyMedium = AppTypography.textTheme.bodyMedium!;
  static  TextStyle bodySmall = AppTypography.textTheme.bodySmall!;
  
  // Arabic text styles
  static const TextStyle arabicLarge = AppTypography.arabicLarge;
  static const TextStyle arabicMedium = AppTypography.arabicMedium;
  static const TextStyle arabicSmall = AppTypography.arabicSmall;
  
  // App-specific text styles
  static const TextStyle chatUserMessage = AppTypography.chatUserMessage;
  static const TextStyle chatAiMessage = AppTypography.chatAiMessage;
  static const TextStyle verseArabic = AppTypography.verseArabic;
  static const TextStyle verseTranslation = AppTypography.verseTranslation;
  static const TextStyle noteTitle = AppTypography.noteTitle;
  static const TextStyle categoryCard = AppTypography.categoryCard;
  static const TextStyle bottomNavLabel = AppTypography.bottomNavLabel;
}

/// Helper methods for common color operations
extension ColorExtensions on Color {
  /// Create a color with the specified opacity
  Color withOpacity(double opacity) {
    return Color.fromRGBO(red, green, blue, opacity);
  }
  
  /// Get a lighter version of this color
  Color lighten([double amount = 0.1]) {
    assert(amount >= 0 && amount <= 1);
    final hsl = HSLColor.fromColor(this);
    final lightness = (hsl.lightness + amount).clamp(0.0, 1.0);
    return hsl.withLightness(lightness).toColor();
  }
  
  /// Get a darker version of this color
  Color darken([double amount = 0.1]) {
    assert(amount >= 0 && amount <= 1);
    final hsl = HSLColor.fromColor(this);
    final lightness = (hsl.lightness - amount).clamp(0.0, 1.0);
    return hsl.withLightness(lightness).toColor();
  }
}

/// Theme-aware widget builder
class ThemeBuilder extends StatelessWidget {
  const ThemeBuilder({
    super.key,
    required this.builder,
  });

  final Widget Function(BuildContext context, bool isDark) builder;

  @override
  Widget build(BuildContext context) {
    return builder(context, context.isDarkTheme);
  }
}

/// Surface color helper widget
class SurfaceContainer extends StatelessWidget {
  const SurfaceContainer({
    super.key,
    required this.child,
    this.level = 'surfaceContainer',
    this.padding,
    this.margin,
    this.borderRadius,
  });

  final Widget child;
  final String level; // surfaceContainer, surfaceContainerHigh, etc.
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final BorderRadius? borderRadius;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: padding,
      margin: margin,
      decoration: BoxDecoration(
        color: context.surfaceColors[level] ?? context.colors.surface,
        borderRadius: borderRadius ?? BorderRadius.circular(12),
      ),
      child: child,
    );
  }
}

/// Arabic text direction helper widget
class ArabicText extends StatelessWidget {
  const ArabicText(
    this.text, {
    super.key,
    this.style,
    this.textAlign = TextAlign.right,
    this.maxLines,
    this.overflow,
  });

  final String text;
  final TextStyle? style;
  final TextAlign textAlign;
  final int? maxLines;
  final TextOverflow? overflow;

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Text(
        text,
        style: style ?? AppTextStyles.arabicLarge,
        textAlign: textAlign,
        maxLines: maxLines,
        overflow: overflow,
      ),
    );
  }
}
