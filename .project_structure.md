# Project Structure Overview

This document describes the folder and file structure of the Flutter project, explaining the purpose of each main directory and notable files. Use this as a reference to replicate the same structure in a new project.

## Main Application Code (lib/)

- **lib/**
  - **main.dart**: Application entry point.
  - **app.dart**: Main app widget and configuration.
  - **init.dart**: Initialization logic.
  - **library.dart**: Barrel file for exports.
  - **color_theme.dart**: Theme and color definitions.
  - **config.dart**: App configuration constants/settings.
  - **static_data.dart**: Hardcoded/static data.

### Feature Modules

- **modules/**: Feature-specific modules, each in its own folder (e.g., `DummyModules`, `Home`, `Station`).
  - **entities/**: Data models/entities for the module.
  - **enums/**: Enum definitions.
  - **data/**: Data providers (e.g., API providers).
  - **widgets/**: UI widgets and controllers for the module.
  - **library.dart**: Barrel file for module exports.

### Screens

- **screens/**: Top-level app screens, each in its own folder (e.g., `HomeScreen`, `SettingsScreen`).
  - **screens/**: Widgets and controllers for the screen.
  - **routes.dart**: Route definitions for the screen.
  - **library.dart**: Barrel file for screen exports.

### UI Components

- **general_widgets/**: Reusable UI widgets (e.g., loading dialogs, snackbars, text widgets).
- **app_drawer/**: App drawer widget and controller.
- **bottombar/**: Bottom navigation bar widget and controller.

### Routing

- **routes.dart**: Main app route definitions.

### State Management & Controllers

- **GetX** is used as the state management solution throughout the project.
- Each screen has its own controller class, which extends `GetxController` (or similar GetX base class).
- Controllers are responsible for managing the state and business logic of their respective screens.
- All services are also managed using GetX, allowing for dependency injection and easy access across the app.
- Controllers and services are typically instantiated and injected using GetX's dependency management features (e.g., `Get.put`, `Get.find`).
- Example controller location: `lib/screens/SomeScreen/screens/some_screen_controller.dart`.

### Services & API Integration

- **services/**: Contains service classes for business logic, data access, and API communication (e.g., `station_services.dart`, `isar_services.dart`).
- Services are registered as dependencies using GetX, making them accessible from controllers and widgets.
- To connect to the API layer:
  - Use a service class (in `lib/services/` or a module's `data/` folder) to encapsulate API calls.
  - Controllers call service methods to fetch or update data, then update the UI state accordingly.
  - Example API provider location: `lib/modules/Station/data/station_api_provider.dart`.

### Utilities

- **utils/**: Utility functions and constants (e.g., `constant.dart`, `general_function.dart`).

### Localization

- **translation/**: Localization files for different languages (e.g., `en.dart`, `ms.dart`, `localization.dart`).

---

## Notes

- Each module, screen, or feature is organized into its own directory for separation of concerns.
- Use barrel files (`library.dart`) for easier imports.
- Platform folders contain native code and resources for their respective platforms.
- Asset folders are organized by type and usage.
- All Dart code resides under `lib/`.
- State management, controller instantiation, and service access are consistently handled using GetX.
- API integration is abstracted into service/provider classes, keeping controllers and UI code clean and focused on state and presentation.

---

This structure supports clean architecture, modularity, and scalability for Flutter projects using GetX for state management and dependency injection. 