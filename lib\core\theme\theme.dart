/// <PERSON><PERSON><PERSON> AI App Theme System
/// 
/// This file exports all theme-related classes and utilities for the app.
/// Import this single file to access the complete design system.
/// 
/// ## Quick Start
/// 
/// 1. **Setup in MaterialApp:**
/// ```dart
/// import 'package:tadabbur_ai_app/core/theme/theme.dart';
/// 
/// MaterialApp(
///   theme: AppTheme.lightTheme,
///   darkTheme: AppTheme.darkTheme,
///   themeMode: ThemeMode.system,
///   // ... rest of your app
/// )
/// ```
/// 
/// 2. **Using colors in widgets:**
/// ```dart
/// Container(
///   color: context.colors.primary,
///   child: Text(
///     'Hello World',
///     style: TextStyle(color: context.colors.onPrimary),
///   ),
/// )
/// ```
/// 
/// 3. **Using text styles:**
/// ```dart
/// Text('Headline', style: context.textStyles.headlineLarge)
/// Text('Body text', style: context.textStyles.bodyMedium)
/// Text('مرحبا', style: context.textStyles.arabicLarge)
/// Text('Chat message', style: context.textStyles.chatUserMessage)
/// ```
/// 
/// 4. **Using surface colors:**
/// ```dart
/// Container(
///   color: context.surfaceColors['surfaceContainer'],
///   // or use the helper widget:
/// )
/// 
/// SurfaceContainer(
///   level: 'surfaceContainerHigh',
///   child: YourWidget(),
/// )
/// ```
/// 
/// 5. **Arabic text with proper direction:**
/// ```dart
/// ArabicText(
///   'النص العربي',
///   style: context.textStyles.verseArabic,
/// )
/// ```
/// 
/// ## Design System Structure
/// 
/// - **Colors**: Material Design 3 color system with light/dark themes
/// - **Typography**: Complete text scale + Arabic support + app-specific styles
/// - **Components**: Pre-configured button, card, input, and navigation themes
/// - **Extensions**: Convenient access methods via BuildContext
/// - **Helpers**: Utility widgets for common patterns
/// 
/// ## Available Text Styles
/// 
/// ### Material Design 3 Scale:
/// - Display: `displayLarge`, `displayMedium`, `displaySmall`
/// - Headline: `headlineLarge`, `headlineMedium`, `headlineSmall`
/// - Title: `titleLarge`, `titleMedium`, `titleSmall`
/// - Label: `labelLarge`, `labelMedium`, `labelSmall`
/// - Body: `bodyLarge`, `bodyMedium`, `bodySmall`
/// 
/// ### Arabic Text:
/// - `arabicLarge` - For Quranic verses and main Arabic content
/// - `arabicMedium` - For translations and secondary Arabic text
/// - `arabicSmall` - For references and tertiary Arabic text
/// 
/// ### App-Specific:
/// - `chatUserMessage` - User messages in chat
/// - `chatAiMessage` - AI responses in chat
/// - `verseArabic` - Quranic verse display
/// - `verseTranslation` - Verse translations (italic)
/// - `noteTitle` - Note and article titles
/// - `categoryCard` - Category card labels
/// - `bottomNavLabel` - Bottom navigation labels
/// 
/// ## Color Categories
/// 
/// ### Primary Colors:
/// - `primary`, `onPrimary`, `primaryContainer`, `onPrimaryContainer`
/// 
/// ### Secondary Colors:
/// - `secondary`, `onSecondary`, `secondaryContainer`, `onSecondaryContainer`
/// 
/// ### Surface Colors:
/// - `surface`, `onSurface`, `surfaceVariant`, `onSurfaceVariant`
/// - Extended: `surfaceContainer`, `surfaceContainerHigh`, etc.
/// 
/// ### Semantic Colors:
/// - `error`, `onError`, `errorContainer`, `onErrorContainer`
/// - `outline`, `outlineVariant`, `shadow`, `scrim`
/// 
/// ## Best Practices
/// 
/// 1. **Always use theme colors** instead of hardcoded colors
/// 2. **Use context extensions** for cleaner code: `context.colors.primary`
/// 3. **Test both light and dark themes** during development
/// 4. **Use semantic color names** (primary, surface) not descriptive ones (green, white)
/// 5. **Use appropriate text styles** for content hierarchy
/// 6. **Use ArabicText widget** for Arabic content to ensure proper RTL layout
/// 
library theme;

// Export all theme components
export 'app_colors.dart';
export 'app_typography.dart';
export 'app_theme.dart';
export 'theme_extensions.dart';
